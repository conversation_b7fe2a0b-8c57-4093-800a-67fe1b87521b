{"version": 3, "file": "install.js", "sourceRoot": "", "sources": ["../../../../src/node/install.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EACL,OAAO,EACP,OAAO,EACP,cAAc,EACd,oBAAoB,EACpB,qBAAqB,GACtB,MAAM,qBAAqB,CAAC;AAE7B,OAAO,EAAC,mBAAmB,EAAC,MAAM,sCAAsC,CAAC;AAEzE,OAAO,EAAC,gBAAgB,EAAC,MAAM,wBAAwB,CAAC;AAExD;;GAEG;AACH,MAAM,iBAAiB,GAAG;IACxB,MAAM,EAAE,QAAQ;IAChB,OAAO,EAAE,iBAAiB;CAClB,CAAC;AAEX;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,eAAe;IACnC,aAAa,EAAE,CAAC;IAEhB,MAAM,aAAa,GAAG,gBAAgB,EAAE,CAAC;IACzC,IAAI,aAAa,CAAC,YAAY,EAAE,CAAC;QAC/B,WAAW,CAAC,mDAAmD,CAAC,CAAC;QACjE,OAAO;IACT,CAAC;IAED,MAAM,eAAe,GAAG,aAAa,CAAC,eAAe,CAAC;IAEtD,MAAM,QAAQ,GAAG,qBAAqB,EAAE,CAAC;IACzC,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;IAC5D,CAAC;IAED,MAAM,OAAO,GAAG,aAAa,CAAC,cAAe,CAAC;IAC9C,MAAM,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAE1C,MAAM,iBAAiB,GACrB,aAAa,CAAC,eAAe,IAAI,mBAAmB,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC;IAC5E,MAAM,sBAAsB,GAC1B,aAAa,CAAC,eAAe;QAC7B,mBAAmB,CAAC,uBAAuB,CAAC;QAC5C,QAAQ,CAAC;IAEX,MAAM,QAAQ,GAAG,aAAa,CAAC,cAAe,CAAC;IAE/C,IAAI,CAAC;QACH,MAAM,gBAAgB,GAAG,EAAE,CAAC;QAE5B,IAAI,aAAa,CAAC,kBAAkB,EAAE,CAAC;YACrC,WAAW,CAAC,kDAAkD,CAAC,CAAC;QAClE,CAAC;aAAM,CAAC;YACN,MAAM,OAAO,GAAG,MAAM,cAAc,CAClC,OAAO,EACP,QAAQ,EACR,iBAAiB,CAClB,CAAC;YACF,gBAAgB,CAAC,IAAI,CACnB,OAAO,CAAC;gBACN,OAAO;gBACP,QAAQ;gBACR,QAAQ;gBACR,OAAO;gBACP,wBAAwB,EAAE,oBAAoB,CAAC,OAAO,EAAE,OAAO,CAAC;gBAChE,OAAO,EAAE,eAAe;gBACxB,YAAY,EACV,OAAO,KAAK,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS;aAChE,CAAC;iBACC,IAAI,CAAC,MAAM,CAAC,EAAE;gBACb,WAAW,CACT,GAAG,iBAAiB,CAAC,OAAO,CAAC,KAAK,MAAM,CAAC,OAAO,mBAAmB,MAAM,CAAC,IAAI,EAAE,CACjF,CAAC;YACJ,CAAC,CAAC;iBACD,KAAK,CAAC,KAAK,CAAC,EAAE;gBACb,MAAM,IAAI,KAAK,CACb,2BAA2B,iBAAiB,CAAC,OAAO,CAAC,KAAK,OAAO,gEAAgE,EACjI;oBACE,KAAK,EAAE,KAAK;iBACb,CACF,CAAC;YACJ,CAAC,CAAC,CACL,CAAC;QACJ,CAAC;QAED,IAAI,OAAO,KAAK,OAAO,CAAC,MAAM,EAAE,CAAC;YAC/B,IAAI,aAAa,CAAC,+BAA+B,EAAE,CAAC;gBAClD,WAAW,CAAC,kDAAkD,CAAC,CAAC;YAClE,CAAC;iBAAM,CAAC;gBACN,MAAM,YAAY,GAAG,MAAM,cAAc,CACvC,OAAO,EACP,QAAQ,EACR,sBAAsB,CACvB,CAAC;gBAEF,gBAAgB,CAAC,IAAI,CACnB,OAAO,CAAC;oBACN,OAAO,EAAE,OAAO,CAAC,mBAAmB;oBACpC,QAAQ;oBACR,QAAQ;oBACR,OAAO,EAAE,YAAY;oBACrB,wBAAwB,EAAE,oBAAoB,CAC5C,OAAO,CAAC,mBAAmB,EAC3B,YAAY,CACb;oBACD,OAAO,EAAE,eAAe;oBACxB,YAAY,EACV,YAAY,KAAK,sBAAsB;wBACrC,CAAC,CAAC,sBAAsB;wBACxB,CAAC,CAAC,SAAS;iBAChB,CAAC;qBACC,IAAI,CAAC,MAAM,CAAC,EAAE;oBACb,WAAW,CACT,GAAG,OAAO,CAAC,mBAAmB,KAAK,MAAM,CAAC,OAAO,mBAAmB,MAAM,CAAC,IAAI,EAAE,CAClF,CAAC;gBACJ,CAAC,CAAC;qBACD,KAAK,CAAC,KAAK,CAAC,EAAE;oBACb,MAAM,IAAI,KAAK,CACb,2BAA2B,OAAO,CAAC,mBAAmB,KAAK,YAAY,gEAAgE,EACvI;wBACE,KAAK,EAAE,KAAK;qBACb,CACF,CAAC;gBACJ,CAAC,CAAC,CACL,CAAC;YACJ,CAAC;QACH,CAAC;QAED,MAAM,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IACtC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACrB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC;AAED,SAAS,gBAAgB,CAAC,OAAiB;IACzC,QAAQ,OAAO,EAAE,CAAC;QAChB,KAAK,QAAQ;YACX,OAAO,OAAO,CAAC,MAAM,CAAC;QACxB,KAAK,SAAS;YACZ,OAAO,OAAO,CAAC,OAAO,CAAC;IAC3B,CAAC;IACD,OAAO,OAAO,CAAC,MAAM,CAAC;AACxB,CAAC;AAED;;GAEG;AACH,SAAS,WAAW,CAAC,UAAmB;IACtC,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAC;IAC1D,MAAM,eAAe,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IAE3E,sCAAsC;IACtC,IAAI,CAAC,eAAe,EAAE,CAAC;QACrB,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC1B,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,aAAa;IACpB,8EAA8E;IAC9E,MAAM,eAAe,GACnB,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IAC3E,MAAM,cAAc,GAClB,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IAC1E,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;IAExD,IAAI,eAAe,EAAE,CAAC;QACpB,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,eAAe,CAAC;IAC/C,CAAC;IACD,IAAI,cAAc,EAAE,CAAC;QACnB,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,cAAc,CAAC;IAC7C,CAAC;IACD,IAAI,YAAY,EAAE,CAAC;QACjB,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,YAAY,CAAC;IACzC,CAAC;AACH,CAAC"}
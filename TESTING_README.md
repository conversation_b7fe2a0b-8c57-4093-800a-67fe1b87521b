# Testing Guide for Bullets Chrome Extension

This document provides comprehensive instructions for running tests and maintaining the test suite for the Bullets Chrome extension.

## Overview

The extension uses Je<PERSON> as the testing framework with jsdom environment for DOM manipulation testing. Tests are organized into three main categories:

- **Unit Tests**: Test individual functions and utilities
- **Integration Tests**: Test interactions between different parts of the extension
- **E2E Tests**: Test the extension in a real browser environment (planned)

## Test Structure

```
__tests__/
├── background.test.js      # Tests for background.js service worker
├── utils.test.js           # Tests for utility functions
├── integration.test.js     # Tests for inter-module communication
└── e2e/                    # End-to-end tests (planned)
    └── puppeteer.test.js
```

## Setup

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

```bash
# Install dependencies
npm install

# Verify Jest is properly installed
npm list jest
```

## Running Tests

### Run All Tests

```bash
npm test
```

### Run Tests in Watch Mode

```bash
npm run test:watch
```

### Run Tests with Coverage Report

```bash
npm run test:coverage
```

### Run Linting

```bash
npm run lint
```

### Fix Linting Issues

```bash
npm run lint:fix
```

## Test Categories

### 1. Unit Tests (`utils.test.js`)

Test individual utility functions:

- Text processing functions
- Error handling functions
- DOM manipulation functions
- Cache management functions

```javascript
describe('Text Processing Functions', () => {
  test('should process text correctly', () => {
    // Test implementation
  });
});
```

### 2. Background Script Tests (`background.test.js`)

Test the service worker functionality:

- Error logging system
- API communication
- Request queuing
- Cache management

```javascript
describe('ErrorLogger', () => {
  test('should log error messages with proper structure', () => {
    // Test error logging
  });
});
```

### 3. Integration Tests (`integration.test.js`)

Test interactions between different parts:

- API communication between content and background scripts
- Storage operations
- Context menu functionality
- Port communication

```javascript
describe('API Communication Integration', () => {
  test('should establish connection with background script', () => {
    // Test port communication
  });
});
```

## Mocking Chrome APIs

The extension uses comprehensive mocks for Chrome APIs in `test-setup.js`:

### Available Mocks

- `chrome.runtime` - Extension runtime APIs
- `chrome.storage` - Local storage APIs
- `chrome.tabs` - Tab management APIs
- `chrome.contextMenus` - Context menu APIs
- `chrome.scripting` - Script injection APIs

### Example Usage

```javascript
// Test storage operations
test('should save data to storage', () => {
  const data = { key: 'value' };
  chrome.storage.local.set(data);

  expect(chrome.storage.local.set).toHaveBeenCalledWith(data, expect.any(Function));
});
```

## Writing Tests

### Test Naming Conventions

- Use descriptive test names: `should [expected behavior] when [condition]`
- Group related tests in `describe` blocks
- Use `beforeEach` and `afterEach` for setup/cleanup

### Example Test Structure

```javascript
describe('Feature Name', () => {
  beforeEach(() => {
    // Setup code
    jest.clearAllMocks();
  });

  test('should handle normal case', () => {
    // Arrange
    const input = 'test input';
    const expected = 'expected output';

    // Act
    const result = functionUnderTest(input);

    // Assert
    expect(result).toBe(expected);
  });

  test('should handle edge case', () => {
    // Test edge cases
  });
});
```

### Testing Async Functions

```javascript
test('should handle async operations', async () => {
  const result = await asyncFunction();
  expect(result).toBeDefined();
});
```

### Testing Error Cases

```javascript
test('should handle errors gracefully', () => {
  expect(() => {
    functionThatThrows();
  }).toThrow('Expected error message');
});
```

## Test Coverage

### Current Coverage Goals

- **Statements**: 80%
- **Branches**: 75%
- **Functions**: 85%
- **Lines**: 80%

### Running Coverage

```bash
npm run test:coverage
```

Coverage reports are generated in the `coverage/` directory.

## Debugging Tests

### Console Output

Tests use mocked console functions. To see actual output:

```javascript
// In test file
console.log('Debug message'); // This will be captured by Jest
```

### Debug Mode

Run Jest with debug flags:

```bash
npm test -- --verbose
npm test -- --debug
```

## Continuous Integration

### GitHub Actions

The project includes a GitHub Actions workflow for automated testing:

```yaml
name: Test
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '16'
      - run: npm ci
      - run: npm test
      - run: npm run lint
```

## Best Practices

### 1. Test Organization

- Keep tests close to the code they test
- Use meaningful test names
- Group related tests together

### 2. Mock Management

- Mock external dependencies
- Use consistent mock patterns
- Clear mocks between tests

### 3. Test Data

- Use realistic test data
- Include edge cases
- Test both success and failure paths

### 4. Performance

- Keep tests fast
- Avoid unnecessary setup
- Use appropriate matchers

## Troubleshooting

### Common Issues

1. **Chrome API not mocked**
   - Ensure `test-setup.js` is properly configured
   - Check that all required APIs are mocked

2. **Tests running slowly**
   - Check for infinite loops in tested code
   - Review async test timeouts
   - Consider using `jest.setTimeout()`

3. **Mock functions not working**
   - Clear mocks between tests: `jest.clearAllMocks()`
   - Use proper mock implementations
   - Check mock function signatures

### Getting Help

- Check Jest documentation: https://jestjs.io/docs/getting-started
- Review Chrome extension testing: https://developer.chrome.com/docs/extensions/mv3/tut_testing/
- Check existing tests for patterns

## Future Enhancements

- [ ] Add end-to-end tests with Puppeteer
- [ ] Implement visual regression testing
- [ ] Add performance testing
- [ ] Set up automated accessibility testing
- [ ] Add contract testing for API endpoints

## Contributing

When adding new features:

1. Write tests first (TDD approach)
2. Ensure all tests pass
3. Update this documentation if needed
4. Maintain or improve test coverage
5. Follow existing code and test patterns

---

For questions or issues related to testing, please check the existing test files or create an issue in the repository.
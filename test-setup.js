// Test setup for Chrome extension testing
// This file configures the test environment to work with Chrome extension APIs

// Mock Chrome API
global.chrome = {
  runtime: {
    getManifest: () => ({
      version: '2.0.0',
      name: 'Bullets - Summarize the web'
    }),
    getURL: (path) => path,
    connect: jest.fn(() => ({
      postMessage: jest.fn(),
      onMessage: {
        addListener: jest.fn()
      },
      onDisconnect: {
        addListener: jest.fn()
      },
      disconnect: jest.fn()
    })),
    onConnect: {
      addListener: jest.fn()
    },
    onMessage: {
      addListener: jest.fn()
    },
    onInstalled: {
      addListener: jest.fn()
    },
    lastError: null,
    sendMessage: jest.fn()
  },
  storage: {
    local: {
      get: jest.fn((keys, callback) => callback({})),
      set: jest.fn((data, callback) => callback && callback())
    }
  },
  tabs: {
    query: jest.fn((queryInfo, callback) => callback([{ id: 1 }])),
    sendMessage: jest.fn(),
    create: jest.fn()
  },
  scripting: {
    executeScript: jest.fn((options, callback) => callback && callback())
  },
  contextMenus: {
    create: jest.fn(),
    removeAll: jest.fn(),
    onClicked: {
      addListener: jest.fn()
    }
  }
};

// Mock fetch API
global.fetch = jest.fn();

// Mock DOM APIs that might not be available
if (!global.window) {
  global.window = {
    location: { href: 'https://example.com' },
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    document: {
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      createElement: jest.fn(() => ({
        style: {},
        classList: {
          add: jest.fn(),
          remove: jest.fn()
        },
        appendChild: jest.fn(),
        remove: jest.fn()
      })),
      querySelector: jest.fn(),
      querySelectorAll: jest.fn(() => [])
    }
  };
}

// Mock navigator.clipboard
if (!global.navigator) {
  global.navigator = {
    clipboard: {
      writeText: jest.fn(() => Promise.resolve())
    }
  };
}

// Console mocks for cleaner test output
const originalConsole = { ...console };
beforeAll(() => {
  console.log = jest.fn();
  console.error = jest.fn();
  console.warn = jest.fn();
  console.info = jest.fn();
});

afterAll(() => {
  Object.assign(console, originalConsole);
});
/**
 * @fileoverview Integration tests for Chrome extension functionality
 * @description Tests that verify the interaction between different parts of the extension
 */

// Mock the extension environment
const mockPort = {
  postMessage: jest.fn(),
  onMessage: {
    addListener: jest.fn()
  },
  onDisconnect: {
    addListener: jest.fn()
  },
  disconnect: jest.fn()
};

describe('API Communication Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    chrome.runtime.connect.mockReturnValue(mockPort);
  });

  test('should establish connection with background script', () => {
    const port = chrome.runtime.connect({ name: 'openai' });
    expect(port).toBe(mockPort);
    expect(chrome.runtime.connect).toHaveBeenCalledWith({ name: 'openai' });
  });

  test('should send summarize request', () => {
    const port = chrome.runtime.connect({ name: 'openai' });
    port.postMessage({ action: 'callOpenAI', text: 'Test text' });

    expect(port.postMessage).toHaveBeenCalledWith({
      action: 'callOpenAI',
      text: 'Test text'
    });
  });

  test('should send title generation request', () => {
    const port = chrome.runtime.connect({ name: 'openai' });
    port.postMessage({ action: 'generateTitle', summary: 'Test summary' });

    expect(port.postMessage).toHaveBeenCalledWith({
      action: 'generateTitle',
      summary: 'Test summary'
    });
  });

  test('should send context request', () => {
    const port = chrome.runtime.connect({ name: 'openai' });
    port.postMessage({ action: 'callOpenAIContext', text: 'Test context' });

    expect(port.postMessage).toHaveBeenCalledWith({
      action: 'callOpenAIContext',
      text: 'Test context'
    });
  });
});

describe('Error Handling Integration', () => {
  test('should handle network errors gracefully', () => {
    const port = chrome.runtime.connect({ name: 'openai' });

    // Simulate error response
    const errorHandler = jest.fn();
    port.onMessage.addListener(errorHandler);

    // Trigger error
    const mockMessage = {
      type: 'error',
      error: 'Network error',
      title: 'Connection Error',
      suggestion: 'Check your internet connection'
    };

    // Get the listener and call it
    const listeners = port.onMessage.addListener.mock.calls;
    expect(listeners.length).toBeGreaterThan(0);

    const listener = listeners[0][0];
    listener(mockMessage);
    expect(errorHandler).toHaveBeenCalledWith(mockMessage);
  });

  test('should handle successful responses', () => {
    const port = chrome.runtime.connect({ name: 'openai' });

    const dataHandler = jest.fn();
    port.onMessage.addListener(dataHandler);

    // Clear any previous calls
    dataHandler.mockClear();

    const mockMessage = {
      type: 'data',
      content: 'Summary content'
    };

    const listeners = port.onMessage.addListener.mock.calls;
    expect(listeners.length).toBeGreaterThan(0);

    const listener = listeners[0][0];
    listener(mockMessage);
    expect(dataHandler).toHaveBeenCalledWith(mockMessage);
  });
});

describe('Storage Integration', () => {
  test('should save settings to storage', () => {
    const settings = {
      model: 'gemini-2.5-flash-lite',
      language: 'french',
      customLanguage: '',
      prompts: {
        summarize: 'Test prompt',
        context: 'Test context prompt',
        title: 'Test title prompt'
      }
    };

    chrome.storage.local.set(settings, jest.fn());

    expect(chrome.storage.local.set).toHaveBeenCalledWith(settings, expect.any(Function));
  });

  test('should retrieve settings from storage', () => {
    const callback = jest.fn();
    chrome.storage.local.get(['bulletsSettings'], callback);

    expect(chrome.storage.local.get).toHaveBeenCalledWith(['bulletsSettings'], expect.any(Function));
  });

  test('should handle storage errors', () => {
    // Mock storage error
    chrome.storage.local.get.mockImplementation((keys, callback) => {
      chrome.runtime.lastError = { message: 'Storage error' };
      callback({});
    });

    const callback = jest.fn();
    chrome.storage.local.get(['test'], callback);

    expect(chrome.runtime.lastError).toBeDefined();
  });
});

describe('Context Menu Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should create context menus on install', () => {
    // Clear mocks before loading
    chrome.contextMenus.removeAll.mockClear();
    chrome.contextMenus.create.mockClear();

    // Load background script to trigger context menu creation
    require('../background.js');

    // Context menus are created inside the onInstalled listener
    // Let's manually trigger the onInstalled event to test the functionality
    const onInstalledListeners = chrome.runtime.onInstalled.addListener.mock.calls;
    if (onInstalledListeners.length > 0) {
      const onInstalledCallback = onInstalledListeners[0][0];
      onInstalledCallback();

      // Now check if context menus were created
      expect(chrome.contextMenus.removeAll).toHaveBeenCalled();
      expect(chrome.contextMenus.create).toHaveBeenCalledTimes(2);
      expect(chrome.contextMenus.create).toHaveBeenCalledWith({
        id: "bulletPage",
        title: "Bullet page",
        contexts: ["all"]
      });
      expect(chrome.contextMenus.create).toHaveBeenCalledWith({
        id: "bulletSelection",
        title: "Bullet selection",
        contexts: ["selection"]
      });
    }
  });

  test('should handle context menu clicks', () => {
    // Load background script first
    require('../background.js');

    // Clear previous calls to scripting.executeScript
    chrome.scripting.executeScript.mockClear();

    const mockInfo = { menuItemId: "bulletPage" };
    const mockTab = { id: 1 };

    // Get the context menu click handler that was added during script load
    const clickHandlerCalls = chrome.contextMenus.onClicked.addListener.mock.calls;
    expect(clickHandlerCalls.length).toBeGreaterThan(0);

    const clickHandler = clickHandlerCalls[0][0];

    // Call the handler
    clickHandler(mockInfo, mockTab);

    // Should execute script in the tab
    expect(chrome.scripting.executeScript).toHaveBeenCalledWith({
      target: { tabId: 1 },
      files: ['content.js']
    });
  });
});